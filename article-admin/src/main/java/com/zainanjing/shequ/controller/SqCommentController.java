package com.zainanjing.shequ.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.core.search.Searchable;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.utils.SecurityUtils;
import com.zainanjing.common.constant.Constants;
import com.zainanjing.common.util.CommonUtil;
import com.zainanjing.shequ.domain.SqComment;
import com.zainanjing.shequ.domain.SqIndexManagerUser;
import com.zainanjing.shequ.domain.SqIndexUserComment;
import com.zainanjing.shequ.service.ISqCommentService;
import com.zainanjing.shequ.service.ISqIndexManagerUserService;
import com.zainanjing.shequ.service.ISqIndexUserCommentService;
import jakarta.annotation.Resource;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 评论Controller
 *
 * <AUTHOR>
 * @date 2024-07-13
 */
@RestController
@RequestMapping("/shequ/comment")
public class SqCommentController extends BaseController {
    @Autowired
    private ISqCommentService sqCommentService;

    @Resource
    private ISqIndexUserCommentService sqIndexUserCommentService;

    @Resource
    private ISqIndexManagerUserService managerUserService;

    /**
     * 查询评论列表
     */
    @PreAuthorize("@ss.hasPermi('shequ:comment:list')")
    @GetMapping("/list")
    public TableDataInfo list(Searchable searchable) {
        long uid = SecurityUtils.getUserId();
        searchable.addSearchParam("uidForStar", uid);
        IPage<SqComment> page = sqCommentService.searchPage(searchable);
        return new TableDataInfo(page.getRecords(), page.getTotal());
    }

    @PreAuthorize("@ss.hasPermi('shequ:comment:audit')")
    @GetMapping(value = "/auditList")
    public TableDataInfo auditList(Searchable searchable) {
        long uid = SecurityUtils.getUserId();
        searchable.addSearchParam("uidForStar", uid);
        searchable.addSearchParam("statusArr", new Integer[]{Constants.WAIT_PASS_AUDIT, Constants.SQ_COMMENT_NOT_PASS});
        IPage<SqComment> page = sqCommentService.searchPage(searchable);
        return new TableDataInfo(page.getRecords(), page.getTotal());
    }

    @Log(title = "评论审核", businessType = BusinessType.UPDATE)
    @PreAuthorize("@ss.hasPermi('shequ:comment:audit')")
    @PostMapping(value = "/audit/{ids}")
    public AjaxResult auditBatch(@PathVariable("ids") List<Long> ids, @RequestBody SqComment sqComment) {
        if (sqCommentService.lambdaUpdate().set(SqComment::getStatus, sqComment.getStatus()).set(SqComment::getRejectReason, sqComment.getRejectReason()).set(SqComment::getAuditTime, CommonUtil.getTimestamp())
                .in(SqComment::getId, ids).update()) {
            //审核通过添加回帖数量
            if (Constants.ZERO.equals(sqComment.getStatus())) {
                addSqCommentNum(ids);
            }
            return success();
        }
        return error();
    }

    /**
     * 审核通过添加回帖数量
     */
    private void addSqCommentNum(List<Long> ids) {
        for (Long id : ids) {
            logger.info("审核通过，添加回帖数量开始");
            SqComment sqComment = sqCommentService.getById(id);
            CommonUtil.redisCommonAdd(Constants.SQPOST + "_" + Constants.COMMENTNUM + "_" + sqComment.getPostId());
            logger.info("审核通过，添加回帖数量结束：" + sqComment.getPostId());
        }
    }


    /**
     * 根据帖子查询所有评论，tree TODO 需要重新测一下
     */
    @PreAuthorize("@ss.hasPermi('shequ:comment:list')")
    @GetMapping("/getCommentTreeByPostId")
    public AjaxResult getCommentTreeByPostId(@RequestParam("postId") Long postId) {
        Map<String, Object> queryMap = new HashMap<>();
        queryMap.put("postId", postId);
        return AjaxResult.success(sqCommentService.searchCommentsTree(queryMap, 0L, 1000L));
    }

    @PreAuthorize("@ss.hasPermi('shequ:comment:list')")
    @GetMapping(value = "/listMyReply")
    public TableDataInfo listMyReply(Searchable searchable) {
        long uid = SecurityUtils.getUserId();
        SqIndexManagerUser managerUser = managerUserService.getById(SecurityUtils.getUserId());
        if (managerUser != null) {
            uid = managerUser.getUid();
        }
        searchable.addSearchParam("uidForStar", uid);
        searchable.addSearchParam("status", Constants.STATUS_NORMAL);
        searchable.addSearchParam("uid", uid);

        IPage<SqComment> page = sqCommentService.searchPage(searchable);
        return new TableDataInfo(page.getRecords(), page.getTotal());
    }

    /**
     * 获取评论详细信息
     */
    @PreAuthorize("@ss.hasPermi('shequ:comment:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id) {
        return AjaxResult.success(sqCommentService.getById(id));
    }

    /**
     * 新增评论
     */
    @PreAuthorize("@ss.hasPermi('shequ:comment:add')")
    @Log(title = "评论", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody SqComment sqComment) {

        SqIndexManagerUser managerUser = managerUserService.getById(SecurityUtils.getUserId());
        if (managerUser != null) {
            sqComment.setUid(managerUser.getUid());
        }

        sqComment.setCreateTime(CommonUtil.getTimestamp());
        sqComment.setUpdateTime(CommonUtil.getTimestamp());
        sqComment.setStatus(0);
        sqComment.setIsAnonymous(0);
        //后台回帖，先设个空默认值 防止报错
        sqComment.setIp("");
        sqComment.setRegion("");
        sqCommentService.saveComment(sqComment);
        String type = sqComment.getType().toString();

        //发系统消息给帖子作者
        //TODO 需要mango db， 暂时不管

        //发系统消息给帖子作者
        //更新是否有最新评论为 1:是
        sqCommentService.updateHasNewComment(null, sqComment.getType().toString(), sqComment.getPostId().toString(), sqComment.getParentId().toString(), Constants.SHI);
        return success();
    }

    /**
     * 修改评论
     */
    @PreAuthorize("@ss.hasPermi('shequ:comment:edit')")
    @Log(title = "评论", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody SqComment sqComment) {
        return toAjax(sqCommentService.lambdaUpdate().set(SqComment::getContent, sqComment.getContent()).eq(SqComment::getId, sqComment.getId()).update());
    }

    /**
     * 删除评论
     */
    @PreAuthorize("@ss.hasPermi('shequ:comment:remove')")
    @Log(title = "评论", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable("ids") List<Long> ids) {
        return toAjax(sqCommentService.lambdaUpdate().set(SqComment::getStatus, Constants.STATUS_DELETE_BY_HOST).in(SqComment::getId, ids).or().in(SqComment::getParentId, ids).update());
    }


    /**
     * 收藏帖子
     */
    @PreAuthorize("@ss.hasPermi('shequ:comment:list')")
    @PostMapping(value = "/star/{id}")
    public AjaxResult star(@PathVariable("id") Long commentId) {
        Long uid = SecurityUtils.getUserId();
        SqIndexUserComment sqIndexUserComment = new SqIndexUserComment();
        sqIndexUserComment.setUid(Long.valueOf(uid).intValue());
        sqIndexUserComment.setCommentId(commentId);
        return toAjax(sqIndexUserCommentService.save(sqIndexUserComment));
    }

    /**
     * 取消收藏帖子
     */
    @PreAuthorize("@ss.hasPermi('shequ:comment:unStar')")
    @PostMapping(value = "/unStar/{id}")
    public AjaxResult unStar(@PathVariable("id") Long commentId) {
        Long uid = SecurityUtils.getUserId();
        return toAjax(sqIndexUserCommentService.lambdaUpdate().eq(SqIndexUserComment::getUid, Long.valueOf(uid).intValue()).eq(SqIndexUserComment::getCommentId, commentId).remove());
    }
}
