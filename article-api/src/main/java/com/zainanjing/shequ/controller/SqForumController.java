package com.zainanjing.shequ.controller;


import com.ruoyi.common.annotation.Anonymous;
import com.ruoyi.common.utils.OSSUtil;
import com.ruoyi.common.utils.ObjUtil;
import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.common.utils.StrUtil;
import com.zainanjing.common.constant.Constants;
import com.zainanjing.common.util.CommonUtil;
import com.zainanjing.convenience.support.web.ResResult;
import com.zainanjing.shequ.domain.*;
import com.zainanjing.shequ.dto.LinkObj;
import com.zainanjing.shequ.service.*;
import jakarta.annotation.Resource;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.*;

import java.util.*;
import java.util.stream.Collectors;

// 原地址: /sqForum
@RestController
@RequestMapping("/api-sq/sqForum")
public class SqForumController {

    private final Logger logger = LoggerFactory.getLogger(SqForumController.class);

    @Resource
    private ISqForumService sqForumService;

    @Resource
    private ISqAreaService sqAreaService;

    @Resource
    private ISqPostService sqPostService;

    @Resource
    private ISqRoleService sqRoleService;

    @Resource
    private ISqIndexUsersRoleService sqIndexUsersRoleService;

    @Resource
    private ISqIndexForumUserService sqIndexForumUserService;

    /**
     * 查看版块列表
     * 原接口/list
     */
    @Anonymous
    @GetMapping("/list")
    public ResResult list(@RequestParam(value = "areaId", defaultValue = "0") Integer areaId) {

        logger.debug("查看版块列表开始，分区id：{}", areaId);
        List<SqArea> aList = new ArrayList<SqArea>();
        //查询出板块列表  这种写法会出现特例：有分区无板块，经过纪总确认，不展示无板块的分区
        List<SqForum> mList;
        logger.debug("第一步 查询版块列表");
        mList = sqForumService.lambdaQuery()
                .eq(SqForum::getIsShow, 1)
                .eq(SqForum::getStatus, 0)
                .eq(!ObjUtil.equals(0, areaId), SqForum::getAreaId, areaId)
                .list();
        if (mList != null && mList.size() > 0) {
            for (SqForum sf : mList) {
                sf.setReplyNum(CommonUtil.redisCommonFind(Constants.SQFORUM + "_" + Constants.REPLYNUM + "_" + sf.getId()));
                sf.setSubjectNum(CommonUtil.redisCommonFind(Constants.SQFORUM + "_" + Constants.SUBJECTNUM + "_" + sf.getId()));
                sf.setTodayPostNum(CommonUtil.redisCommonFind(Constants.SQFORUM + "_" + Constants.TODAYPOSTNUM + "_" + sf.getId()));
            }
            //进行转换
            if (ObjUtil.equals(0, areaId)) {
                //查询分区列表
                logger.debug("第二步 查询分区列表");
                aList = sqAreaService.lambdaQuery()
                        .eq(SqArea::getStatus, 0)
                        .eq(SqArea::getIsShow, 1)
                        .orderByDesc(SqArea::getSort)
                        .list();
                if (aList != null && aList.size() > 0) {
                    for (SqArea sa : aList) {
                        List<SqForum> sfList = new ArrayList<SqForum>();
                        for (SqForum sf : mList) {
                            if (sf.getAreaId().equals(sa.getId())) {
                                sfList.add(sf);
                                //对图片做个处理
                                if (!StrUtil.isEmpty(sf.getLogo())) {
                                    sf.setLogo(OSSUtil.getImageURL(sf.getLogo()));
                                }
                                if (!StrUtil.isEmpty(sf.getImgUrl())) {
                                    sf.setImgUrl(OSSUtil.getImageURL(sf.getImgUrl()));
                                }
                                //1：客户端要求方便解析
                                //2：不用每个字段都塞在最外层。层次清晰
                                sf.setLinkObj(new LinkObj(sf.getLinkType(), sf.getLinkModule(), sf.getResourceId(), sf.getLinkTo(), sf.getLinkUrl()));
                            }
                        }
                        sa.setSqForumList(sfList);
                    }
                }
            } else {
                //表示查询某一个分区下的板块列表
                SqArea sa = sqAreaService.getById(areaId);
                for (SqForum sf : mList) {
                    sf.setReplyNum(CommonUtil.redisCommonFind(Constants.SQFORUM + "_" + Constants.REPLYNUM + "_" + sf.getId()));
                    sf.setSubjectNum(CommonUtil.redisCommonFind(Constants.SQFORUM + "_" + Constants.SUBJECTNUM + "_" + sf.getId()));
                    sf.setTodayPostNum(CommonUtil.redisCommonFind(Constants.SQFORUM + "_" + Constants.TODAYPOSTNUM + "_" + sf.getId()));
                    if (!StrUtil.isEmpty(sf.getLogo())) {
                        sf.setLogo(OSSUtil.getImageURL(sf.getLogo()));
                    }
                    if (!StrUtil.isEmpty(sf.getImgUrl())) {
                        sf.setImgUrl(OSSUtil.getImageURL(sf.getImgUrl()));
                    }
                    //1：客户端要求方便解析
                    //2：不用每个字段都塞在最外层。层次清晰
                    sf.setLinkObj(new LinkObj(sf.getLinkType(), sf.getLinkModule(), sf.getResourceId(), sf.getLinkTo(), sf.getLinkUrl()));
                }
                sa.setSqForumList(mList);
                aList.add(sa);
            }

        }
        return ResResult.success(aList);
    }


    /**
     * 查看板块详情
     */
    @Anonymous
    @GetMapping("/findById")
    public ResResult findById(@RequestParam("id") Long id) {
        Long uid = SecurityUtils.getUserId();
        SqForum sm = sqForumService.getById(id);
        if (sm != null) {
            if (!StrUtil.isEmpty(sm.getLogo())) {
                sm.setLogo(OSSUtil.getImageURL(sm.getLogo()));
            }
            if (!StrUtil.isEmpty(sm.getImgUrl())) {
                sm.setImgUrl(OSSUtil.getImageURL(sm.getImgUrl()));
            }
            sm.setReplyNum(CommonUtil.redisCommonFind(Constants.SQFORUM + "_" + Constants.REPLYNUM + "_" + sm.getId()));
            sm.setSubjectNum(CommonUtil.redisCommonFind(Constants.SQFORUM + "_" + Constants.SUBJECTNUM + "_" + sm.getId()));
            sm.setTodayPostNum(CommonUtil.redisCommonFind(Constants.SQFORUM + "_" + Constants.TODAYPOSTNUM + "_" + sm.getId()));
            //查询收藏信息
            configCollect(sm, id);
            //查询置顶帖信息
            Map<String, Object> map = new HashMap<String, Object>();
            map.put("forumIdForTop", id);
            map.put("areaIdForTop", sm.getAreaId());
            map.put("isTop", Constants.YES);
            map.put("status", "0");
            map.put("allTop", Constants.YES);
            //置顶帖排序
            map.put("orderBy", "4");
            map.put("forumStatus", Constants.STATUS_NORMAL);

            List<SqPost> spList = sqPostService.search(map, 0, 10);
            sm.setTopPostList(spList);
            return ResResult.success(sm);

        } else {
            logger.error("用户 uid={}获取社区讨论版详情出错，不存在该讨论版。id={}", uid, id);
            return ResResult.success(new SqForum());
        }


    }

    /**
     * 板块校验
     */
    @Anonymous
    @GetMapping("/check")
    public ResResult check(@RequestParam("id") Long id,//板块ID或者帖子id
                           @RequestParam("type") String type,
                           @RequestParam("flag") String flag//0表示板块  1表示帖子
    ) {
        Long uid = SecurityUtils.getUserId();

        SqForum sm = null;
        if (flag.equals("0")) {
            //板块
            sm = (SqForum) sqForumService.getById(id);
        } else if (flag.equals("1")) {
            //帖子
            Optional<SqPost> postOpt = sqPostService.getOptById(id);
            if (postOpt.isPresent()) {
                sm = (sqForumService.getById(postOpt.get().getForumId()));
            }
        }

        if (sm != null && sm.getId() != null && sm.getId() > 0) {
            //校验开始
            if (ObjUtil.isNotNull(uid)) {
                //新增判断  如果当前用户是管理员  则直接通过
                //如果当前用户是版主，并且操作的是当前板块，是的话 直接通过
                boolean isSystem = false;
                Set<Integer> roleIds = sqIndexUsersRoleService.lambdaQuery().eq(SqIndexUsersRole::getUid, uid).list()
                        .stream().map(SqIndexUsersRole::getRoleId).collect(Collectors.toSet());
                List<SqRole> roleList = sqRoleService.lambdaQuery().in(SqRole::getId, roleIds).list();
                if (roleList != null && roleList.size() > 0) {
                    for (SqRole role : roleList) {
                        if (role.getIsSystem() != null && (role.getIsSystem() == 1 || role.getIsSystem() == 2)) {
                            isSystem = true;
                            break;

                        }
                    }
                }
                if (isSystem) {
                    return ResResult.success();
                } else {
                    //不是系统管理员  则判断是不是当前版主
                    List<SqIndexForumUser> list_temp = sqIndexForumUserService.lambdaQuery()
                            .eq(SqIndexForumUser::getUid, uid)
                            .eq(SqIndexForumUser::getForumId, sm.getId()).list();
                    if (list_temp != null && list_temp.size() > 0) {
                        return ResResult.success();
                    }
                }
            }

            //TODO 根据标签获取权限 和纪总确认只保留 实名制+标签, 发帖和回帖一个权限，描述不同而已
            //此处如果uid为空之类的  会报错
            if (type.equals("0")) {
                //校验浏览板块的权限
                ;//浏览是否需要实名
                sm.getIsLabelBrowse();//浏览是否需要标签
                if (ObjUtil.equals(1, sm.getIsCertificationBrowse())) {
                    //TODO 权限无法通过
                    return ResResult.error(30016, "访问该版块需先实名认证哦");
                }
                if (ObjUtil.equals(1, sm.getIsLabelBrowse())) {
                    //TODO 权限无法通过
                    return ResResult.error(30019, "访问该版块需持有xx标签哦");
                }
                //TODO 验证标签匹配+是否实名
            } else {
                String msg = "发表帖子";
                if (type.equals("1")) {
                    msg = "发表帖子";
                } else if (type.equals("2")) {
                    msg = "回复帖子";
                }
                if (ObjUtil.equals(1, sm.getIsCertificationPost())) {
                    //TODO 权限无法通过
                    return ResResult.error(30016, msg + "需先实名认证哦");
                }
                if (ObjUtil.equals(1, sm.getIsLabelPost())) {
                    //TODO 权限无法通过
                    return ResResult.error(30019, msg + "需持有xx标签哦");
                }
            }
            return ResResult.success();
        } else {
            return ResResult.error(36001, "用户 uid=" + uid + "校验社区讨论版详情出错，不存在该讨论版。id=" + id);
        }
    }


    @PostMapping("/collect")
    public ResResult collect() {
        Long uid = SecurityUtils.getUserId();
        logger.error("用户查询收藏的社区板块开始，uid=" + uid);
//		initPage();//暂时不考虑分页
        Map<String, Object> paramMap = new HashMap<String, Object>();
        paramMap.put("uid", uid);
        List<SqForum> mList = new ArrayList<SqForum>();
        mList = sqForumService.collectList(paramMap, 0, Integer.MAX_VALUE);
        if (mList != null && mList.size() > 0) {
            for (SqForum sf : mList) {
                if (!StrUtil.isEmpty(sf.getLogo())) {
                    sf.setLogo(OSSUtil.getImageURL(sf.getLogo()));
                }
                if (!StrUtil.isEmpty(sf.getImgUrl())) {
                    sf.setImgUrl(OSSUtil.getImageURL(sf.getImgUrl()));
                }
                sf.setReplyNum(CommonUtil.redisCommonFind(Constants.SQFORUM + "_" + Constants.REPLYNUM + "_" + sf.getId()));
                sf.setSubjectNum(CommonUtil.redisCommonFind(Constants.SQFORUM + "_" + Constants.SUBJECTNUM + "_" + sf.getId()));
                sf.setTodayPostNum(CommonUtil.redisCommonFind(Constants.SQFORUM + "_" + Constants.TODAYPOSTNUM + "_" + sf.getId()));
            }
        }
        //返回aList
        return ResResult.success(mList);

    }

    //TODO 统一收藏夹，后续处理
    private void configCollect(SqForum sf, String forumId) {
        if (SecurityUtils.getUserId() == null) {
            sf.setIsCollect(Constants.FOU);
            return;
        }
        Map filterMap = new HashMap<>();
        filterMap.put("ctype", Integer.parseInt(Constants.SQ_FORUM_COLLECT));
        filterMap.put("siteMemberId", Integer.parseInt(uid));
        filterMap.put("collectObjectId", Integer.parseInt(forumId));
        List<SiteCollect> siteCollectList = siteCollectService.search(filterMap, 0, Integer.MAX_VALUE);
        if (siteCollectList.isEmpty()) {
            sf.setIsCollect(Constants.FOU);
        } else {
            sf.setIsCollect(Constants.SHI);
            SiteCollect siteCollect = siteCollectList.get(0);
            sf.setCollectedid(siteCollect.getSiteCollectId());
        }
    }


}
