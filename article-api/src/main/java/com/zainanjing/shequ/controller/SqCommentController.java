package com.zainanjing.shequ.controller;

import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.ruoyi.common.annotation.Anonymous;
import com.ruoyi.common.dto.EventAction;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.event.CommonEvent;
import com.ruoyi.common.exception.ServiceException;
import com.ruoyi.common.utils.*;
import com.ruoyi.common.utils.ip.AddressUtils;
import com.ruoyi.common.utils.ip.IpUtils;
import com.zainanjing.article.domain.BcImg;
import com.zainanjing.article.domain.GdmmParams;
import com.zainanjing.article.service.IGdmmImgService;
import com.zainanjing.article.service.IGdmmParamsService;
import com.zainanjing.common.constant.Constants;
import com.zainanjing.common.constant.RedisKey;
import com.zainanjing.common.util.CommonUtil;
import com.zainanjing.convenience.support.mongo.MangoSqMessageService;
import com.zainanjing.convenience.support.mongo.SqMessage;
import com.zainanjing.convenience.support.web.ResResult;
import com.zainanjing.convenience.utils.WangyiCheckUtil;
import com.zainanjing.shequ.domain.*;
import com.zainanjing.shequ.mapper.GdmmUsersMapper;
import com.zainanjing.shequ.mapper.SqCommentMapper;
import com.zainanjing.shequ.mapper.SqPostMapper;
import com.zainanjing.shequ.mapper.SqRoleMapper;
import com.zainanjing.shequ.service.ISqCommentService;
import com.zainanjing.shequ.service.ISqIndexForumUserService;
import com.zainanjing.shequ.service.ISqPostService;
import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletRequest;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.web.bind.annotation.*;

import java.math.BigDecimal;
import java.util.*;

/**
 * 帖子评论Controller
 *
 * <AUTHOR>
 * @date 2024-07-13
 */
@RestController
@RequestMapping("/api-sq/sqComment")
public class SqCommentController {

    private final Logger logger = LoggerFactory.getLogger(SqCommentController.class);

    @Resource
    private ISqPostService sqPostService;

    @Resource
    private IGdmmParamsService gdmmParamsService;

    @Resource
    private ISqCommentService sqCommentService;

    @Resource
    private ApplicationEventPublisher applicationEventPublisher;

    @Resource
    private IGdmmImgService gdmmImgService;

    @Resource
    private GdmmUsersMapper gdmmUsersMapper;

    @Resource
    private MangoSqMessageService mangoSqMessageService;

    @Resource
    private WangyiCheckUtil wangyiCheckUtil;

    @Resource
    private SqPostMapper sqPostMapper;

    @Resource
    private ISqIndexForumUserService sqIndexForumUserService;

    @Resource
    private SqRoleMapper sqRoleMapper;

    @Resource
    private SqCommentMapper sqCommentMapper;

    /**
     * 原接口 /siteapp/sqComment/list
     */
    @Anonymous
    @GetMapping("/list")
    public ResResult list(@RequestParam("postId") Long postId, @RequestParam("type") Integer type,// 1帖子下评论 2评论及子评论 3只看子评论
                          @RequestParam(value = "commentId", required = false) Long commentId, @RequestParam(value = "latestId", required = false) Long latestId, @RequestParam(value = "limitType", required = false) String limitType, @RequestParam(value = "orderBy", required = false) String orderBy, @RequestParam(value = "currentPage", defaultValue = "1") Integer currentPage, @RequestParam(value = "pageSize", defaultValue = "10") Integer pageSize) {
        ResResult resultList = ResResult.success();

        Long uid = SecurityUtils.getUserId();
        HashMap<String, Object> filterMap = new HashMap<String, Object>();
        filterMap.put("postId", postId);
        filterMap.put("sessionUid", uid);//记录当前用户来查询屏蔽用户
        //分页用
        if (StrUtil.isNotEmpty(limitType)) {
            filterMap.put("limitType", limitType);
            filterMap.put("latestId", latestId);
        }
        //排序
        if (StrUtil.isNotEmpty(orderBy)) {
            filterMap.put("orderBy", orderBy);
        }


        SqPost sqPost = sqPostMapper.findById(postId);
        //版主可见贴  && 没有版主权限    && 不是系统管理员
        if (!isHasSqForumPermission(Long.valueOf(sqPost.getForumId())) && sqPost.getSeeLevel().equals(1) && !isSystemAdmin()) {
            filterMap.put("uid", uid);
        }

        IPage page = Page.of(currentPage, pageSize);
        //设置查询帖子的状态
        filterMap.put("status", Constants.STATUS_NORMAL);
        List<SqComment> sqCommentList = null;
        if (type == 2) {
            filterMap.put("isFor", "app");
            sqCommentList = sqCommentService.searchCommentsTree(filterMap, page.offset(), page.getSize());
        } else if (type == 3) {
            filterMap.put("parentId", commentId);
            filterMap.put("type", Constants.TYPE_REPLAY_COMMENT);
            filterMap.put("start", page.offset());
            filterMap.put("size", page.getSize());
            sqCommentList = sqCommentMapper.list(filterMap);
            sqCommentList = commentsAddImages(sqCommentList, postId);
        }

        //20240415获取点赞信息
        sqCommentService.praiseInfo(uid, sqCommentList);
        resultList.setData(sqCommentList);
        return resultList;
    }

    private List<SqComment> commentsAddImages(List<SqComment> sqCommentList, Long postId) {
        List<BcImg> bcImgList = gdmmImgService.lambdaQuery().eq(BcImg::getPostId, postId)
                .eq(BcImg::getType, Constants.IMAGE_TYPE_SQCOMMENT)
                .eq(BcImg::getStatus, Constants.STATUS_NORMAL).list();

        for (SqComment bcComment : sqCommentList) {
            String imgUrl = bcComment.getImgUrl();
            Integer avaType = bcComment.getAvatarType();
            imgUrl = CommonUtil.commonFindPicPath(avaType, imgUrl);
            bcComment.setImgUrl(imgUrl);
            for (BcImg bcImg : bcImgList) {
                bcImg.setImgUrl(OSSUtil.getImageURL(bcImg.getImgUrl()));
                if (bcComment.getId().equals(bcImg.getCommentId())) {
                    bcComment.getBcImgList().add(bcImg);
                }
            }
        }
        return sqCommentList;
    }


    /**
     * 原接口 /siteapp/sqComment/listByUid
     */
    /**
     * @Title:listByUid
     * @Description 个人主页 社区帖子列表
     * void
     */
    @Anonymous
    public ResResult listByUid(@RequestParam("uid") Long uid,
                               @RequestParam(value = "currentPage", defaultValue = "1") Integer currentPage,
                               @RequestParam(value = "pageSize", defaultValue = "10") Integer pageSize) {
        Long uidSelf = SecurityUtils.getUserId();

        HashMap<String, Object> filterMap = new HashMap<String, Object>();
        filterMap.put("uid", uid);
        filterMap.put("status", Constants.FOU);
        //都可见
        filterMap.put("seeLevel", 0);
        filterMap.put("uidSelf", uidSelf);
        List<SqComment> sqCommentList = sqCommentMapper.searchPage(Page.of(currentPage, pageSize, false), filterMap).getRecords();
        //评论里所有帖子id  用于给评论设置 帖子图片
        List<Long> sqPostIdList = new ArrayList<Long>();
        List<Long> sqCommentIdList = new ArrayList<Long>();
        for (SqComment sqComment : sqCommentList) {
            sqPostIdList.add(sqComment.getPostId());
            sqCommentIdList.add(sqComment.getId());
        }

        if (sqPostIdList.size() > 0) {
            List<BcImg> bcImgList = gdmmImgService.lambdaQuery().in(BcImg::getPostId, sqPostIdList)
                    .eq(BcImg::getType, Constants.IMAGE_TYPE_SQPOST)
                    .eq(BcImg::getStatus, Constants.STATUS_NORMAL).list();
            //评论里的帖子图片
            for (BcImg bcImg : bcImgList) {
                bcImg.setImgUrl(OSSUtil.getImageURL(bcImg.getImgUrl()));
                for (SqComment sqComment : sqCommentList) {
                    if (bcImg.getPostId().equals(sqComment.getPostId())) {
                        sqComment.getSqPostImgList().add(bcImg);
                    }
                }
            }
        }

        if (sqCommentIdList.size() > 0) {
            List<BcImg> bcImgList = gdmmImgService.lambdaQuery().in(BcImg::getCommentId, sqCommentIdList)
                    .eq(BcImg::getType, Constants.IMAGE_TYPE_SQCOMMENT)
                    .eq(BcImg::getStatus, Constants.STATUS_NORMAL).list();
            for (BcImg bcImg : bcImgList) {
                bcImg.setImgUrl(OSSUtil.getImageURL(bcImg.getImgUrl()));
                for (SqComment sqComment : sqCommentList) {
                    if (sqComment.getId().equals(bcImg.getCommentId())) {
                        sqComment.getBcImgList().add(bcImg);
                    }
                }
            }
        }
        //20240415获取点赞信息
        sqCommentService.praiseInfo(uid, sqCommentList);
        return ResResult.success(sqCommentList);
    }


    //    原接口 /siteapp/sqComment/listMyReply
    @GetMapping("/listMyReply")
    public ResResult listMyReply(@RequestParam("id") Long id,
                                 @RequestParam(value = "currentPage", defaultValue = "1") Integer currentPage,
                                 @RequestParam(value = "pageSize", defaultValue = "10") Integer pageSize) {
        Long uid = SecurityUtils.getUserId();
        HashMap<String, Object> filterMap = new HashMap<String, Object>();
        filterMap.put("uid", uid);
        if (ObjUtil.isNotNull(id)) {
            //表示uid用户查询id用户的主页
            filterMap.put("uid", id);
        }
        List<SqComment> sqCommentList = sqCommentMapper.searchListMyReply(Page.of(currentPage, pageSize, false), filterMap).getRecords();
        //20240415获取点赞信息
        sqCommentService.praiseInfo(uid, sqCommentList);
        if (CollUtil.isNotEmpty(sqCommentList)) {
            for (SqComment sqComment : sqCommentList) {
                String imgUrl = sqComment.getImgUrl();
                Integer avaType = sqComment.getAvatarType();
                imgUrl = CommonUtil.commonFindPicPath(avaType, imgUrl);
                sqComment.setImgUrl(imgUrl);
                sqComment.setPostImgUrl(OSSUtil.getImageURL(sqComment.getPostImgUrl()));
            }
        }
        return ResResult.success(sqCommentList);
    }

    //    原接口 /siteapp/sqComment/listReplyMe
    @GetMapping("/listReplyMe")
    public ResResult listReplyMe(@RequestParam(value = "currentPage", defaultValue = "1") Integer currentPage, @RequestParam(value = "pageSize", defaultValue = "10") Integer pageSize) {
        Long uid = SecurityUtils.getUserId();
        HashMap<String, Object> filterMap = new HashMap<String, Object>();
        filterMap.put("uid", uid);
        List<SqComment> sqCommentList = sqCommentMapper.searchListReplyMe(Page.of(currentPage, pageSize, false), filterMap).getRecords();
        if (CollUtil.isNotEmpty(sqCommentList)) {
            if (sqCommentList != null && sqCommentList.size() > 0) {
                for (SqComment sqComment : sqCommentList) {
                    String imgUrl = sqComment.getImgUrl();
                    Integer avaType = sqComment.getAvatarType();
                    imgUrl = CommonUtil.commonFindPicPath(avaType, imgUrl);
                    sqComment.setImgUrl(imgUrl);
                }
            }
        }
        //当用户点击 查看 ”回复我的“ 时 更新是否有最新评论为 0:否
        sqCommentService.updateHasNewComment(uid, null, null, null, Constants.FOU);
        //20240415获取点赞信息
        sqCommentService.praiseInfo(uid, sqCommentList);
        return ResResult.success(sqCommentList);
    }

//    原接口 /siteapp/sqComment/hasNewComment

    /**
     * 的帖子和评论是否有新的评论
     */
    @GetMapping("/hasNewComment")
    public ResResult hasNewComment() {
        Map map = new HashMap();
        Long uid = SecurityUtils.getUserId();

        if (sqPostService.lambdaQuery().eq(SqPost::getUid, uid).eq(SqPost::getHasNewComment, Constants.SHI).exists()) {
            return ResResult.success(Map.of("hasNewComment", Constants.SHI));
        }
        if (sqCommentService.lambdaQuery().eq(SqComment::getUid, uid).eq(SqComment::getHasNewComment, Constants.SHI).exists()) {
            return ResResult.success(Map.of("hasNewComment", Constants.SHI));
        }
        return ResResult.success(Map.of("hasNewComment", Constants.FOU));
    }


//    原接口 /siteapp/sqComment/delete

    /**
     * 删除评论
     */
    @PostMapping("/delete")
    public ResResult delete(@RequestParam("id") Long id) {
        Long uid = SecurityUtils.getUserId();
        SqComment sqComment = sqCommentService.getById(id);
        if (sqComment == null) {
            return ResResult.error("评论不存在");
        }
        LambdaUpdateWrapper<SqComment> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.set(SqComment::getUpdateTime, CommonUtil.getTimestamp());
        if (ObjUtil.equals(uid, sqComment.getUid())) {//帖子是本人的
            updateWrapper.set(SqComment::getStatus, Constants.STATUS_DELETE);
            updateWrapper.eq(SqComment::getUid, uid);
        } else if (isHasSqForumPermission(Long.valueOf(sqComment.getForumId()))) {//版主删除
            updateWrapper.set(SqComment::getStatus, Constants.STATUS_DELETE_BY_HOST);
        } else if (isSystemAdmin()) {//系统删除
            updateWrapper.set(SqComment::getStatus, Constants.STATUS_DELETE_BY_HOST);
        } else {
            return ResResult.error(3601, "非本人发布的帖子，无权删除！");
        }
        updateWrapper.and(x -> x.eq(SqComment::getId, id).or().eq(SqComment::getParentId, id));
        return ResResult.success(sqCommentService.update(updateWrapper));
    }


    /**
     * content
     * isAnonymous
     * postId
     * type
     */
    @PostMapping("/save")
    public ResResult save(HttpServletRequest request) {
        Long uid = SecurityUtils.getUserId();
        //帖子id【必填】
        String postId = request.getParameter("postId");
        //内容【必填】
        String content = request.getParameter("content");
        //类型【必填】
        String type = request.getParameter("type");
        //评论id【选填】【如果是对评论进行评论必填】
        String commentId = request.getParameter("commentId");

        String bcImgIds = request.getParameter("bcImgIds");

        String isAnonymous = request.getParameter("isAnonymous");

        //20240118 发帖记录ip并转换为省市区 主要要做到转换失败了，发帖也要成功
        String ip = IpUtils.getIpAddr(request);
        String region = AddressUtils.getRealAddressByIP(ip);

        if (StrUtil.isEmpty(postId)) {
            return ResResult.error(24151, "帖子id不能为空");
        }
        if (StrUtil.isEmpty(type)) {
            return ResResult.error(24152, "类型不能为空");
        }
        if (StrUtil.isEmpty(content)) {
            return ResResult.error(24152, "内容不能为空");
        }

        wangyiCheckUtil.checkText("SqCommentAction_save", content);

        SqComment sqComment = new SqComment();
        sqComment.setUid(uid);
        sqComment.setPostId(Long.valueOf(postId));
        sqComment.setContent(content);
        sqComment.setType(Integer.valueOf(type));
        sqComment.setLevel(Integer.valueOf(type));
        sqComment.setCreateTime(CommonUtil.getTimestamp());
        sqComment.setUpdateTime(CommonUtil.getTimestamp());
        sqComment.setStatus(Integer.valueOf(Constants.STATUS_NORMAL));
        //根据回帖设置，调整状态
        Integer sqCommentIsClose = Integer.parseInt(CommonUtil.redisCommonFind(RedisKey.SQ_COMMENT_IS_CLOSE.getKey()) + "");
        Integer sqCommentIsAudit = Integer.parseInt(CommonUtil.redisCommonFind(RedisKey.SQ_COMMENT_IS_AUDIT.getKey()) + "");
        //是否关闭回帖
        if (Constants.SHI.equals(sqCommentIsClose)) {
            return ResResult.error(80002, "社区回帖已关闭");
        }
        //是否需要审核
        sqComment.setStatus(Constants.SHI.equals(sqCommentIsAudit) ? Constants.WAIT_PASS_AUDIT : Constants.ZERO);

        //评论 插入版块id
        SqPost sqPost = sqPostService.getById(postId);
        sqComment.setForumId(sqPost.getForumId());

        if (StrUtil.isNotEmpty(commentId)) {
            sqComment.setParentId(Long.valueOf(commentId));
        } else {
            sqComment.setParentId(0L);
        }

        if (StrUtil.isNotEmpty(isAnonymous)) {
            sqComment.setIsAnonymous(Integer.valueOf(isAnonymous));
        } else {
            sqComment.setIsAnonymous(0);
        }

        validatePostTime(uid);

        sqComment.setIp(ip);
        sqComment.setRegion(region);
        if (StrUtil.isNotBlank(bcImgIds)) {
            String[] pics = bcImgIds.split("_");
            List<BcImg> bcImgList = new ArrayList<>();
            for (String url : pics) {
                BcImg bcImg = new BcImg();
                bcImg.setImgUrl(url);
            }
            sqComment.setBcImgList(bcImgList);
        }
        sqCommentService.saveComment(sqComment);
        searchImgUrlsAndUserHead(sqComment);

        applicationEventPublisher.publishEvent(new CommonEvent(sqComment, EventAction.builder().operator(SecurityUtils.getLoginUser()).type(BusinessType.INSERT).build()));

        logger.info("提交帖子评论");
        //更新是否有最新评论为 1:是
        sqCommentService.updateHasNewComment(null, type, postId, commentId, Constants.SHI);

        //发系统消息给帖子作者
        AsyncManager.me().execute(() -> {
            SqMessage sqMessage = null;
            String imgUrl = "";
            //消息里显示的 对方uid 对方昵称  对方头像
            Long otherUid = SecurityUtils.getUserId();
            String otherName = "";
            String otherImgUrl = "";
            GdmmUsers gdmmUsers = gdmmUsersMapper.selectUser(otherUid);
            if (gdmmUsers != null) {
                sqComment.setMedalLevel(gdmmUsers.getMedalLevel());
                otherName = gdmmUsers.getUserName();
                otherImgUrl = CommonUtil.commonFindPicPath(gdmmUsers.getAvatarType(), gdmmUsers.getImageUrl());
            }
            //从社区帖子表htmlImg读取第一张图
            if (sqPost.getHtmlImg() != null && sqPost.getHtmlImg().length() > 0) {
                String[] imgList = sqPost.getHtmlImg().split(",,");
                imgUrl = imgList[0];
            }

            if ("1".equals(type)) {
                sqMessage = mangoSqMessageService.initSqMessageSq("SQPOST", sqPost.getId(), sqPost.getSubject(), sqPost.getContent(), BigDecimal.ZERO, imgUrl, otherUid, otherName, otherImgUrl);
                String sqContent = "评论了你的帖子" + sqPost.getSubject();
                mangoSqMessageService.saveMongodbSqMessage(sqMessage, Constants.SQMESSAGE_TYPE_SQ, Constants.SQMESSAGE_CODE_COMMENT, sqPost.getUid().longValue(), content);
            } else if ("2".equals(type)) {
                SqComment tempComment = sqCommentService.getById(commentId);
                if (tempComment != null) {
                    Long receiverUid = tempComment.getUid();
                    sqMessage = mangoSqMessageService.initSqMessageSq("SQCOMMENT", sqPost.getId(), sqPost.getSubject(), sqPost.getContent(), BigDecimal.ZERO, imgUrl, otherUid, otherName, otherImgUrl);
                    String sqContent = "回复了你的评论";
                    mangoSqMessageService.saveMongodbSqMessage(sqMessage, Constants.SQMESSAGE_TYPE_SQ, Constants.SQMESSAGE_CODE_COMMENT, receiverUid, content);
                }
            }
        });
        return ResResult.success(sqComment);
    }

    /**
     * 评论点赞
     */
    @PostMapping("/praise")
    public ResResult praise(HttpServletRequest request) {
        Long uid = SecurityUtils.getUserId();
        String commentId = request.getParameter("commentId");
        //类型【必填】 0赞1取消赞   ===社区保持一致
        String status = request.getParameter("status");

        if (!NumberUtil.isInteger(commentId)) {
            ResResult.error(30020, "入参有误");
        }

        Set<String> values = Set.of("0", "1");//0 取消点赞，1点赞
        if (!values.contains(status)) {
            ResResult.error(30020, "入参有误");
        }

        String uid_commentId_key = RedisKey.SQ_COMMENT_PRAISE.getKeyForSqComment(String.valueOf(uid), commentId);
        String commentId_key = RedisKey.SQ_COMMENT_PRAISE_NUM.getKey(commentId);
        Long value = CommonUtil.redisCommonFind(uid_commentId_key);
        //点赞
        if (Constants.YES.equals(status)) {
            if (value == 1l) {
                throw new ServiceException("该社区评论已经点赞");
            } else {
                CommonUtil.redisCommonPut(uid_commentId_key, Constants.YES);
                Long praiseNum = CommonUtil.redisCommonAdd(commentId_key);
                logger.error("社区评论点赞------------redis +1-----------:commentId:{},点赞数：{}", commentId, praiseNum);
                SqComment sqComment = new SqComment();
                sqComment.setId(Long.parseLong(commentId));
                applicationEventPublisher.publishEvent(new CommonEvent(sqComment, EventAction.builder().operator(SecurityUtils.getLoginUser()).type(BusinessType.LIKE).build()));

            }
            //取消点赞
        } else if (Constants.NO.equals(status)) {
            if (value == 1l) {
                CommonUtil.redisCommonPut(uid_commentId_key, Constants.NO);
                CommonUtil.redisCommonMinus(commentId_key);
                logger.error("社区评论取消点赞------------redis -1-----------:commentId:{}", commentId);
            } else {
                throw new ServiceException("该社区评论已经取消点赞");
            }
        }
        return ResResult.success();
    }


    private void validatePostTime(Long uid) {
        List<GdmmParams> gdmmParamList = gdmmParamsService.lambdaQuery().eq(GdmmParams::getModule, "SQ").eq(GdmmParams::getCode, "COMMENT_INTERVAL").list();
        if (gdmmParamList.size() > 0) {
            Integer commentAnterval = Integer.valueOf(gdmmParamList.get(0).getValue());
            List<SqComment> comments = sqCommentService.lambdaQuery().eq(SqComment::getUid, uid).orderByDesc(SqComment::getId).last("limit 1").list();
            if (comments.size() > 0) {
                if (CommonUtil.getTimestamp() - comments.get(0).getCreateTime() < commentAnterval) {
                    throw new ServiceException("您的回复太快了");
                }
            }
        }

    }

    private void searchImgUrlsAndUserHead(SqComment sqComment) {
        List<BcImg> bcImglist = gdmmImgService.lambdaQuery().eq(BcImg::getCommentId, sqComment.getId()).eq(BcImg::getStatus, Constants.STATUS_NORMAL).eq(BcImg::getType, Constants.IMAGE_TYPE_SQCOMMENT).last("limit 6").list().stream().map(bcImg -> {
            bcImg.setImgUrl(OSSUtil.getImageURL(bcImg.getImgUrl()));
            return bcImg;
        }).toList();

        GdmmUsers gdmmUsers = gdmmUsersMapper.selectUser(SecurityUtils.getUserId());
        if (ObjUtil.isNotNull(gdmmUsers)) {
            sqComment.setImgUrl(OSSUtil.getImageURL(gdmmUsers.getImageUrl()));
        }
        //end
        sqComment.setBcImgList(bcImglist);
    }


    private boolean isHasSqForumPermission(Long forumId) {
        return sqIndexForumUserService.lambdaQuery().eq(SqIndexForumUser::getForumId, forumId).eq(SqIndexForumUser::getUid, SecurityUtils.getUserId()).exists();
    }

    private boolean isSystemAdmin() {
        List<SqRole> sqRoles = sqRoleMapper.selectRoleByUserId(SecurityUtils.getUserId());
        if (CollUtil.isNotEmpty(sqRoles)) {
            if (sqRoles.stream().anyMatch(role -> Set.of(1, 2).contains(role.getIsSystem()))) {
                return true;
            }
        }
        return false;
    }
}
