package com.zainanjing.shequ.controller;

import com.ruoyi.common.dto.EventAction;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.event.CommonEvent;
import com.ruoyi.common.exception.ServiceException;
import com.ruoyi.common.utils.*;
import com.ruoyi.common.utils.ip.AddressUtils;
import com.ruoyi.common.utils.ip.IpUtils;
import com.zainanjing.article.domain.BcImg;
import com.zainanjing.article.domain.GdmmSpecialWord;
import com.zainanjing.article.service.IGdmmImgService;
import com.zainanjing.article.service.IGdmmSpecialWordService;
import com.zainanjing.common.constant.Constants;
import com.zainanjing.common.constant.RedisKey;
import com.zainanjing.common.util.CommonUtil;
import com.zainanjing.convenience.support.web.ResResult;
import com.zainanjing.convenience.utils.WangyiCheckUtil;
import com.zainanjing.shequ.domain.SqComment;
import com.zainanjing.shequ.domain.SqIndexForumUser;
import com.zainanjing.shequ.domain.SqPost;
import com.zainanjing.shequ.domain.SqRole;
import com.zainanjing.shequ.dto.LinkObj;
import com.zainanjing.shequ.dto.SqPostDTO;
import com.zainanjing.shequ.mapper.SqPostMapper;
import com.zainanjing.shequ.mapper.SqRoleMapper;
import com.zainanjing.shequ.service.ISqCommentService;
import com.zainanjing.shequ.service.ISqIndexForumUserService;
import com.zainanjing.shequ.service.ISqPostService;
import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletRequest;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.web.bind.annotation.*;

import java.io.UnsupportedEncodingException;
import java.net.URLDecoder;
import java.sql.SQLException;
import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * 帖子数据Controller
 *
 * <AUTHOR>
 * @date 2024-07-13
 */
@RestController
@RequestMapping("/api-sq/sqPost")
public class SqPostController {

    private final Logger logger = LoggerFactory.getLogger(SqPostController.class);

    @Resource
    private ISqPostService sqPostService;

    @Resource
    private IGdmmSpecialWordService gdmmSpecialWordService;

    @Resource
    private ApplicationEventPublisher applicationEventPublisher;

    @Resource
    private ISqIndexForumUserService sqIndexForumUserService;

    @Resource
    private SqRoleMapper sqRoleMapper;

    @Resource
    private WangyiCheckUtil wangyiCheckUtil;

    @Resource
    private SqPostMapper sqPostMapper;

    @Resource
    private ISqCommentService sqCommentService;

    @Resource
    private IGdmmImgService gdmmImgService;

    /**
     * 保存
     */
    @PostMapping("/save")
    public ResResult save(HttpServletRequest request) {
        Long uid = SecurityUtils.getUserId();
        String forumId = request.getParameter("forumId");
        String color = request.getParameter("color");
        String subject = request.getParameter("subject");
        String content = request.getParameter("content");
        String html = request.getParameter("html");
        logger.debug("社区发帖标题=={}社区发帖原文html=={}", subject, html);
        try {
            html = URLDecoder.decode(html, "utf-8");
            logger.debug("社区发帖decode标题=={}社区发帖decode原文html=={}", subject, html);
        } catch (UnsupportedEncodingException e1) {
            logger.error("error原文html=={}", html, e1);
        }
        String isAnonymous = request.getParameter("isAnonymous");
        String isRec = request.getParameter("isRec");
        String isTop = request.getParameter("isTop");
        String bcImgIds = request.getParameter("bcImgIds");
        String seeLevel = request.getParameter("seeLevel");
        String type = Constants.POST_TYPE_COMMON;
        String haveHidden = request.getParameter("haveHidden");
        String hiddenContent = request.getParameter("hiddenContent");
        String goodsId = request.getParameter("goodsId");

        String checkContent = StrUtil.nullToEmpty(subject) + StrUtil.nullToEmpty(content) + StrUtil.nullToEmpty(html);
        wangyiCheckUtil.checkText("SqPostAction_save", checkContent);
        if (StrUtil.isNotBlank(html)) {
            String checkHtml = getImageListFromHtml(html);
            wangyiCheckUtil.checkImages(checkHtml);
        }

        //20240118 发帖记录ip并转换为省市区 主要要做到转换失败了，发帖也要成功
        String ip = IpUtils.getIpAddr(request);
        String region = AddressUtils.getRealAddressByIP(ip);
        logger.debug("社区发帖ip=={},region=={}", ip, region);

        if (StrUtil.isEmpty(forumId)) {
            return ResResult.error(24122, "讨论版id不能为空");
        }
        if (StrUtil.isEmpty(goodsId)) {
            goodsId = "0";
        }
        //如果为空货没有版主权限设置默认值
        if (StrUtil.isEmpty(color) || !(isHasSqForumPermission(Long.valueOf(forumId)) || isSystemAdmin())) {
            color = "0";
        }
        if (StrUtil.isEmpty(subject)) {
            subject = "";
        }
        if (StrUtil.isEmpty(isRec) || !(isHasSqForumPermission(Long.valueOf(forumId)) || isSystemAdmin())) {
            isRec = Constants.NO;
        }

        if (StrUtil.isEmpty(isTop) || !(isHasSqForumPermission(Long.valueOf(forumId)) || isSystemAdmin())) {
            isTop = Constants.NO;
        }
        if (StrUtil.isEmpty(seeLevel) || !(isHasSqForumPermission(Long.valueOf(forumId)) || isSystemAdmin())) {
            seeLevel = "0";
        }
        if (StrUtil.isEmpty(content)) {
            content = "";
        }
        if (StrUtil.isEmpty(html)) {
            html = "";
        }
        if (StrUtil.isEmpty(isAnonymous)) {
            isAnonymous = "0";
        }
        if (isHasSqForumPermission(Long.valueOf(forumId)) || isSystemAdmin()) {
            type = Constants.POST_TYPE_CPMPERE;
        }
        if (StrUtil.isEmpty(haveHidden)) {
            haveHidden = "0";
        }
        if (StrUtil.isEmpty(hiddenContent)) {
            hiddenContent = "";
        }
        SqPost sqPost = new SqPost();
        sqPost.setForumId(Integer.valueOf(forumId));
        sqPost.setSubject(subject);
        sqPost.setContent(content);
        sqPost.setHtml(html);
        sqPost.setColor(color);
        sqPost.setUid(Long.valueOf(uid).intValue());
        sqPost.setClickNum(0L);
        sqPost.setInitClickNum(0L);
        sqPost.setCommentNum(0L);
        sqPost.setPraiseNum(0L);
        sqPost.setType(Integer.valueOf(type));
        sqPost.setSort(0L);
        sqPost.setIsRec(Integer.valueOf(isRec));
        sqPost.setIsTop(Integer.valueOf(isTop));
        sqPost.setCreateTime(CommonUtil.getTimestamp());
        sqPost.setUpdateTime(CommonUtil.getTimestamp());
        sqPost.setStatus(0);
        sqPost.setSeeLevel(Integer.valueOf(seeLevel));
        sqPost.setIsAnonymous(Integer.valueOf(isAnonymous));
        sqPost.setCanReply(0);
        sqPost.setHasNewComment(0);
        sqPost.setIsHot(0);
        sqPost.setVideoImg("");
        sqPost.setVideoUrl("");
        sqPost.setTopType(1);
        sqPost.setHaveHidden(Integer.valueOf(haveHidden));
        sqPost.setHiddenContent(hiddenContent);
        sqPost.setAttribute(0);
        sqPost.setGoodsId(Integer.parseInt(goodsId));
        sqPost.setIsShowIndex(Constants.FOU);//默认显示
        List<String> sensitiveWords = gdmmSpecialWordService.lambdaQuery().select(GdmmSpecialWord::getName).list().stream().map(GdmmSpecialWord::getName).toList();
        //富文本里的图片转义, 再进行敏感词校验  防止 图片名有敏感词 比如 awp   20210615
        String regex = "<[^>]*>";
        String htmlReplace = html.replaceAll(regex, "").replaceAll("&nbsp;", "").replaceAll("\\s*|\t|\r|\n", "");
        logger.error("社区发帖decode标题=={}社区发帖decode转义后html=={}", subject, htmlReplace);
        for (String string : sensitiveWords) {
            if (htmlReplace.contains(string)) {
                return ResResult.error(10002, "帖子内容中含有敏感词 '" + string + "'，提交失败");
            }
        }
//			validatePostTime(uid);
        //根据发帖设置，调整状态
        Integer sqPostIsClose = Integer.parseInt(CommonUtil.redisCommonFind(RedisKey.SQ_POST_IS_CLOSE.getKey()) + "");
        Integer sqPostIsAudit = Integer.parseInt(CommonUtil.redisCommonFind(RedisKey.SQ_POST_IS_AUDIT.getKey()) + "");
        //是否关闭发帖
        if (Constants.SHI.equals(sqPostIsClose)) {
            return ResResult.error(80001, "社区发帖已关闭");
        }
        //是否需要审核
        sqPost.setStatus(Constants.SHI.equals(sqPostIsAudit) ? Constants.SQ_WAIT_AUDIT : Constants.ZERO);
        sqPost.setIp(ip);
        sqPost.setRegion(region);
        if (StrUtil.isNotBlank(bcImgIds)) {
            String[] pics = bcImgIds.split("_");
            List<BcImg> bcImgList = new ArrayList<>();
            for (String url : pics) {
                BcImg bcImg = new BcImg();
                bcImg.setImgUrl(url);
            }
            sqPost.setBcImgList(bcImgList);
        }

        sqPostService.savePost(sqPost);
        applicationEventPublisher.publishEvent(new CommonEvent(sqPost, EventAction.builder()
                .operator(SecurityUtils.getLoginUser())
                .type(BusinessType.INSERT)
                .build()));

        return ResResult.success();
    }

    //原调用时post调用
    @GetMapping("/findById")
    public ResResult findById(@RequestParam("postId") Long postId, @RequestParam("orderBy") String orderBy) {
        Long uid = SecurityUtils.getUserId();
        SqPostDTO sqPost = sqPostMapper.findById(postId);
        //20250114帖子详情接口增加判空
        if (sqPost == null || !Constants.STATUS_NORMAL.equals(sqPost.getStatus().toString())) {
            throw new ServiceException("帖子已删除或不存在");
        }
        LinkObj linkObj = new LinkObj();
        linkObj.setLinkModule(sqPost.getLinkModule());
        linkObj.setLinkTo(sqPost.getLinkTo());
        linkObj.setLinkType(sqPost.getLinkType());
        linkObj.setLinkUrl(sqPost.getLinkUrl());
        linkObj.setResourceId(sqPost.getResourceId());
        sqPost.setLinkObj(linkObj);
        sqPost.setGoodsThumbnail(OSSUtil.getImageURL(sqPost.getGoodsThumbnail()));
        checkPostIsHost(Arrays.asList(sqPost));
        //by chenrong   头像获取逻辑
        String imgUrl = sqPost.getImgUrl();
        Integer avaType = sqPost.getAvatarType();
        imgUrl = CommonUtil.commonFindPicPath(avaType, imgUrl);
        sqPost.setImgUrl(imgUrl);
        //end

        sqPost.setClickNum(CommonUtil.redisCommonFind(Constants.SQPOST + "_" + Constants.CLICKNUM + "_" + sqPost.getId()).longValue() + sqPost.getInitClickNum());
        sqPost.setPraiseNum(CommonUtil.redisCommonFind(Constants.SQPOST + "_" + Constants.PRAISENUM + "_" + sqPost.getId()).longValue());
        sqPost.setCommentNum(CommonUtil.redisCommonFind(Constants.SQPOST + "_" + Constants.COMMENTNUM + "_" + sqPost.getId()).longValue());

        sqPost.setForumLogo(OSSUtil.getImageURL(sqPost.getForumLogo()));
        sqPost.setVideoImg(OSSUtil.getImageURL(sqPost.getVideoImg()));
        List<BcImg> sqPostImgList = searchPostImgs(postId);
        sqPost.setBcImgList(sqPostImgList);

        //帖子下的评论树
        Map<String, Object> filterMap = new HashMap<String, Object>();
        //如果为版主可见 且 没有没版主权限
        if (sqPost.getSeeLevel().equals(1) && !isHasSqForumPermission(Long.valueOf(sqPost.getForumId())) && !isSystemAdmin()) {
            filterMap.put("uid", uid);
        }

        filterMap.put("postId", postId);
        if (StrUtil.isNotEmpty(orderBy)) filterMap.put("orderBy", orderBy);
        filterMap.put("isFor", "app");
        filterMap.put("sessionUid", uid);//记录当前用户来查询屏蔽用户
        filterMap.put("status", Constants.STATUS_NORMAL);
        //TODO 未完成功能
        List<SqComment> sqCommentList = sqCommentService.searchCommentsTree(filterMap, 0L, 10L);
        sqPost.setSqCommentList(sqCommentList);
        CommonUtil.redisCommonAdd(Constants.SQPOST + "_" + Constants.CLICKNUM + "_" + postId);

        Boolean isSqPostNeedSetHot = CommonUtil.isSqPostNeedSetHot(String.valueOf(postId), sqPost.getIsHot());
        if (isSqPostNeedSetHot) {
            sqPostService.lambdaUpdate().eq(SqPost::getId, postId)
                    .set(SqPost::getIsHot, 2).update();
        }

        if (ObjUtil.isNotNull(uid)) {
            //是否收藏 TODO 收藏是通用表功能，后续补充
//            queryIsCollect(uid, postId, sqPost);
            //是否评论
            sqPost.setHasComment(hasComment(uid, postId));
            //20240415帖子点赞信息
            praiseInfo(uid, sqPost);
        }
        //20250417帖子重新查询评论数，不再从缓存取
        Long total = sqCommentService.lambdaQuery().eq(SqComment::getPostId, sqPost.getId())
                .eq(SqComment::getType, Constants.TYPE_REPLAY_POST)
                .eq(SqComment::getStatus, Constants.STATUS_NORMAL)
                .count();
        sqPost.setCommentNum(total.longValue());
        //更新redis缓存的评论数
        CommonUtil.redisCommonPut(Constants.SQPOST + "_" + Constants.COMMENTNUM + "_" + sqPost.getId(), total.toString());
        return ResResult.success(sqPost);
    }

    @PostMapping("/praise")
    public ResResult praise(HttpServletRequest request) {

        Long uid = SecurityUtils.getUserId();
        String postId = request.getParameter("postId");
        //类型【必填】 0赞1取消赞   ===社区保持一致
        String status = request.getParameter("status");

        if (!NumberUtil.isInteger(postId)) {
            ResResult.error(30020, "入参有误");
        }

        Set<String> values = Set.of("0", "1");//0 取消点赞，1点赞
        if (!values.contains(status)) {
            ResResult.error(30020, "入参有误");
        }

        String uid_postId_key = RedisKey.SQ_POST_PRAISE.getKeyForSqPost(String.valueOf(uid), postId);
        String postId_key = RedisKey.SQ_POST_PRAISE_NUM.getKey(postId);

        Long value = CommonUtil.redisCommonFind(uid_postId_key);

        //点赞
        if (Constants.YES.equals(status)) {
            if (value == 1l) {
                throw new ServiceException("该帖子已经点赞");
            } else {
                CommonUtil.redisCommonPut(uid_postId_key, Constants.YES);
                Long praiseNum = CommonUtil.redisCommonAdd(postId_key);
                logger.debug("帖子点赞------------redis +1-----------:postId:{},点赞数：{}", postId, praiseNum);
                SqPost sqPost = new SqPost();
                sqPost.setId(Long.valueOf(postId));
                applicationEventPublisher.publishEvent(new CommonEvent(sqPost, EventAction.builder()
                        .operator(SecurityUtils.getLoginUser())
                        .type(BusinessType.LIKE)
                        .build()));
            }
            //取消点赞
        } else if (Constants.NO.equals(status)) {
            if (value == 1l) {
                CommonUtil.redisCommonPut(uid_postId_key, Constants.NO);
                CommonUtil.redisCommonMinus(postId_key);
                logger.debug("帖子取消点赞------------redis -1-----------:postId:{}", postId);
            } else {
                throw new ServiceException("该帖子已经取消点赞");
            }
        }
        return ResResult.success();
    }

    private boolean isHasSqForumPermission(Long forumId) {
        return sqIndexForumUserService.lambdaQuery().
                eq(SqIndexForumUser::getForumId, forumId)
                .eq(SqIndexForumUser::getUid, SecurityUtils.getUserId()).exists();
    }

    private boolean isSystemAdmin() {
        List<SqRole> sqRoles = sqRoleMapper.selectRoleByUserId(SecurityUtils.getUserId());
        if (CollUtil.isNotEmpty(sqRoles)) {
            if (sqRoles.stream().anyMatch(role -> Set.of(1, 2).contains(role.getIsSystem()))) {
                return true;
            }
        }
        return false;
    }

    private String getImageListFromHtml(String html) {
        StringBuffer sb = new StringBuffer();
        List<String> imgList = getImgSrcNew(html);
        if (imgList != null && !imgList.isEmpty()) {
            for (String img : imgList) {
                sb.append(img);
                sb.append(",");
            }
            sb.deleteCharAt(sb.toString().length() - 1);
        }
        return sb.toString();

    }

    private List<String> getImgSrcNew(String htmlStr) {
        if (htmlStr == null) {
            return null;
        }
        String goods = "<section[^>]*?>[\\s\\S]*?<\\/section>";
        htmlStr = htmlStr.replaceAll(goods, "");
        String img = "";
        List<String> pics = new ArrayList<String>();
        String regEx_img = "<img.*src\\s*=\\s*(.*?)[^>]*?>";
        Pattern p_image = Pattern.compile(regEx_img, Pattern.CASE_INSENSITIVE);
        Matcher m_image = p_image.matcher(htmlStr);
        while (m_image.find()) {
            img = img + "," + m_image.group();
            Matcher m = Pattern.compile("src\\s*=\\s*\"?(.*?)(\"|>|\\s+)").matcher(img);
            while (m.find()) {
                if (m.group(1).contains("http") || m.group(1).contains("https")) {
                    pics.add(m.group(1));
                }
            }
        }
        return pics;
    }


    private List<SqPostDTO> checkPostIsHost(List<SqPostDTO> sqPostList) {
        List<SqIndexForumUser> sqIndexForumUsers = sqIndexForumUserService.list();
        for (SqPostDTO sqPost : sqPostList) {
            for (SqIndexForumUser sqIndexForumUser : sqIndexForumUsers) {
                if (Long.valueOf(sqPost.getUid()).equals(sqIndexForumUser.getUid())) {
                    sqPost.setIsHost(1);
                    break;
                } else {
                    sqPost.setIsHost(0);
                }
            }
        }
        return sqPostList;
    }

    private List<BcImg> searchPostImgs(Long postId) {
        List<BcImg> sqPostImgList = gdmmImgService.lambdaQuery()
                .eq(BcImg::getPostId, postId)
                .eq(BcImg::getType, Constants.IMAGE_TYPE_SQPOST)
                .eq(BcImg::getStatus, Constants.STATUS_NORMAL)
                .orderByAsc(BcImg::getSort)
                .list();
        if (CollUtil.isNotEmpty(sqPostImgList)) {
            sqPostImgList.forEach(bcImg -> {
                bcImg.setImgUrl(OSSUtil.getImageURL(bcImg.getImgUrl()));
            });
        }
        return sqPostImgList;
    }

    //TODO 用到了通用功能
//    private SqPost queryIsCollect(Long uid, String postId, SqPost sqPost) throws SQLException {
//        Map<String, Object> filterMap = new HashMap<String, Object>();
//        filterMap.put("ctype", Integer.parseInt(Constants.SQ_POST_COLLECT));
//        filterMap.put("siteMemberId", Integer.parseInt(uid));
//        filterMap.put("collectObjectId", Integer.parseInt(postId));
//        List<SiteCollect> siteCollectList = siteCollectService.search(filterMap, 0, Integer.MAX_VALUE);
//        if (siteCollectList.isEmpty()) {
//            sqPost.setIsCollect(Constants.FOU);
//            return sqPost;
//        } else {
//            sqPost.setIsCollect(Constants.SHI);
//            sqPost.setCollectedid(siteCollectList.get(0).getSiteCollectId());
//            return sqPost;
//        }
//    }

    private Integer hasComment(Long uid, Long postId){
        boolean exists = sqCommentService
                .lambdaQuery().eq(SqComment::getPostId, postId)
                .eq(SqComment::getType, Constants.TYPE_REPLAY_POST)
                .eq(SqComment::getUid, uid).exists();
        if (exists) {
            return Constants.FOU;
        } else {
            return Constants.SHI;
        }
    }

    private void praiseInfo(Long uid,SqPostDTO sqPost) {
        if(sqPost == null){
            return;
        }
        //是否点赞
        if(ObjUtil.isNotNull(uid)){
            String uid_postId_key = RedisKey.SQ_POST_PRAISE.getKeyForVideo(uid.toString(), sqPost.getId().toString());
            Long isPraise = CommonUtil.redisCommonFind(uid_postId_key);
            sqPost.setIsPraise(isPraise.intValue());
        }
        //点赞数
        String postId_key = RedisKey.SQ_POST_PRAISE_NUM.getKey(sqPost.getId().toString());
        Long praiseNum = CommonUtil.redisCommonFind(postId_key);
        sqPost.setPraiseNum(praiseNum);
    }

}
