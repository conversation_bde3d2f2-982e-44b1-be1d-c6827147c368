package com.zainanjing.shequ.mapper;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.ruoyi.common.core.mapper.GenericMapper;
import com.zainanjing.shequ.domain.SqComment;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

/**
 * 评论Mapper接口
 *
 * <AUTHOR>
 * @date 2024-07-13
 */
public interface SqCommentMapper extends GenericMapper<SqComment> {

    List<SqComment> list(Map<String,Object> params);

    IPage<SqComment> searchPage(IPage<SqComment> page, @Param("p") Map<String, Object> p);

    /**
     * 搜索回复我的评论列表
     *
     * @param params 查询参数
     * @return 评论列表
     */
    IPage<SqComment> searchListReplyMe(IPage<SqComment> page, @Param("p") Map<String, Object> params);

    /**
     * 搜索我的回复列表
     *
     * @param params 查询参数
     * @return 评论列表
     */
    IPage<SqComment> searchListMyReply(IPage<SqComment> page,@Param("p") Map<String, Object> params);

}
