package com.zainanjing.shequ.service.impl;

import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.fasterxml.jackson.databind.JsonNode;
import com.ruoyi.common.core.page.Pageable;
import com.ruoyi.common.core.search.Searchable;
import com.ruoyi.common.core.service.impl.GenericCurdServiceImpl;
import com.ruoyi.common.utils.AsyncManager;
import com.ruoyi.common.utils.OSSUtil;
import com.ruoyi.common.utils.http.HttpUtil;
import com.zainanjing.article.domain.BcImg;
import com.zainanjing.article.mapper.GdmmImgMapper;
import com.zainanjing.article.service.IGdmmImgService;
import com.zainanjing.common.constant.Constants;
import com.zainanjing.common.constant.RedisKey;
import com.zainanjing.common.util.CommonUtil;
import com.zainanjing.shequ.domain.SqComment;
import com.zainanjing.shequ.domain.SqPost;
import com.zainanjing.shequ.mapper.SqCommentMapper;
import com.zainanjing.shequ.mapper.SqPostMapper;
import com.zainanjing.shequ.service.ISqCommentService;
import com.zainanjing.shequ.service.ISqPostService;
import jakarta.annotation.Resource;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * 评论Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-07-13
 */
@Service
public class SqCommentServiceImpl extends GenericCurdServiceImpl<SqCommentMapper, SqComment>
        implements ISqCommentService {

    @Autowired
    private IGdmmImgService gdmmImgService;

    @Resource
    private SqPostMapper sqPostMapper;

    @Resource
    private GdmmImgMapper gdmmImgMapper;

    @Resource
    private ISqPostService sqPostService;

    @Resource
    private SqCommentMapper sqCommentMapper;

    public List<SqComment> searchCommentsTree(Map<String, Object> filterMap,
                                              Long startRow, Long pageSize) {
        //设置查询帖子的状态
        filterMap.put("status", Constants.STATUS_NORMAL);
        //屏蔽列表 CLOSE_UID_xxx  模糊查询符合的keys
        Set<String> closeUids = CommonUtil.getCloseUids(filterMap.get("sessionUid"));
        filterMap.put("closeUids", closeUids);

        //帖子下评论的图片
        List<BcImg> bcImgList = gdmmImgService.lambdaQuery().eq(BcImg::getPostId, filterMap.get("postId")).eq(BcImg::getStatus, Constants.STATUS_NORMAL).eq(BcImg::getType, Constants.IMAGE_TYPE_SQCOMMENT).list();
        bcImgList = addAliImgPath(bcImgList);

        //父评论列表
        filterMap.put("type", Constants.TYPE_REPLAY_POST);
        filterMap.put("start", startRow);
        filterMap.put("size", pageSize);
        List<SqComment> sqCommentList = sqCommentMapper.list(filterMap);
        sqCommentList = commentAddImg(sqCommentList, bcImgList);

        //子评论列表
        filterMap.put("type", Constants.TYPE_REPLAY_COMMENT);
        filterMap.put("orderBy", 1);
        String isFor = (String) filterMap.get("isFor");
        List<SqComment> commentToCommentList = sqCommentMapper.searchPage(Page.of(-1, 0, false), filterMap).getRecords();
        commentToCommentList = commentAddImg(commentToCommentList, bcImgList);
        //20250416获取点赞信息
        praiseInfo((Long) filterMap.get("sessionUid"), sqCommentList);
        praiseInfo((Long) filterMap.get("sessionUid"), commentToCommentList);
        //子列表并入父列表
        sqCommentList = commentAddSonComment(sqCommentList, commentToCommentList, isFor);
        return sqCommentList;
    }


    @Override
    public IPage<SqComment> searchPage(Searchable searchable) {
        Pageable pageable = searchable.getPage();
        Page pagination = new Page(pageable.getPageNumber(), searchable.getIsPage() ? pageable.getPageSize() : -1);
        return baseMapper.searchPage(pagination, searchable.getFilterCdtns());
    }

    @Override
    public void updateHasNewComment(Long uid, String type, String postId, String commentId, Integer hasNewComment) {
        AsyncManager.me().execute(() -> {
            //这里用于点击 “回复我的” 去掉红点
            if (uid != null && uid > 0) {
                this.lambdaUpdate().eq(SqComment::getUid, uid).set(SqComment::getHasNewComment, hasNewComment).update();
                LambdaUpdateWrapper<SqPost> sqPostWrapper = new LambdaUpdateWrapper<>();
                sqPostWrapper.set(SqPost::getHasNewComment, hasNewComment).eq(SqPost::getUid, uid);
                sqPostMapper.update(sqPostWrapper);

                this.lambdaUpdate().eq(SqComment::getUid, uid).set(SqComment::getHasNewComment, hasNewComment).update();

                //这里用于当有新回帖时 让我的主题里  的“回复我的” 出现红点
            } else if (Constants.TYPE_POST.equals(type)) {
                LambdaUpdateWrapper<SqPost> sqPostWrapper = new LambdaUpdateWrapper<>();
                sqPostWrapper.set(SqPost::getHasNewComment, hasNewComment).eq(SqPost::getId, postId);
                sqPostMapper.update(sqPostWrapper);
                //这里用于当有新回帖时 让我的主题里  的“回复我的” 出现红点
            } else if (Constants.TYPE_COMMENT.equals(type)) {
                this.lambdaUpdate().eq(SqComment::getId, commentId).set(SqComment::getHasNewComment, hasNewComment).update();
            }
        });
    }

    @Override
    public void praiseInfo(Long uid, List<SqComment> sqCommentList) {
        if (sqCommentList == null) {
            return;
        }
        for (SqComment item : sqCommentList) {
            //是否点赞
            if (uid != null) {
                String uid_postId_key = RedisKey.SQ_COMMENT_PRAISE.getKeyForSqComment(uid.toString(), item.getId().toString());
                Long isPraise = CommonUtil.redisCommonFind(uid_postId_key);
                item.setIsPraise(isPraise.intValue());
            }
            //点赞数
            String postId_key = RedisKey.SQ_COMMENT_PRAISE_NUM.getKey(item.getId().toString());
            Long praiseNum = CommonUtil.redisCommonFind(postId_key);
            item.setPraiseNum(praiseNum);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean saveComment(SqComment sqComment) {
        boolean res = this.save(sqComment);
        //如果是对帖子的评论
        if (sqComment.getType().equals(1)) {
            //计算楼层
            Long floor = this.lambdaQuery().eq(SqComment::getPostId, sqComment.getPostId())
                    .eq(SqComment::getType, 1).lt(SqComment::getId, sqComment.getId()).count();

            //更新评论楼层
            this.lambdaUpdate().eq(SqComment::getId, sqComment.getId()).set(SqComment::getFloor, floor).update();

            CommonUtil.redisCommonAdd(Constants.SQPOST + "_" + Constants.COMMENTNUM + "_" + sqComment.getPostId());
            sqPostService.updatePostCommentNum(sqComment.getPostId());
            SqPost sqPost = sqPostMapper.selectById(sqComment.getPostId());

            CommonUtil.redisCommonAdd(Constants.SQFORUM + "_" + Constants.REPLYNUM + "_" + sqPost.getForumId());
            CommonUtil.redisCommonAdd(Constants.SQFORUM + "_" + Constants.TODAYPOSTNUM + "_" + sqPost.getForumId());
        }
        int sort = 1;
        for (BcImg bcImg : sqComment.getBcImgList()) {
            bcImg.setCommentId(sqComment.getId());
            setImgSize(bcImg);
            bcImg.setPostId(sqComment.getPostId());
            bcImg.setStatus(Integer.valueOf(Constants.STATUS_NORMAL));
            bcImg.setType(Long.valueOf(Constants.IMAGE_TYPE_SQCOMMENT));
            bcImg.setSort(sort);
            gdmmImgMapper.insert(bcImg);
            sort++;
        }
        return res;

    }

    /**
     * 获取图片宽高
     */
    private void setImgSize(BcImg sourceImg) {
        JsonNode restNode = HttpUtil.get(OSSUtil.getImageURL(sourceImg.getImgUrl()) + "@info");
        sourceImg.setWidth(restNode.get("width").asInt());
        sourceImg.setHeight(restNode.get("height").asInt());
    }

    /**
     * @param @param bcImgList
     * @return List<BcImg>    返回类型
     * @throws
     * @Title: addAliImgPath
     * @Description: 图片拼接阿里云地址
     */
    private List<BcImg> addAliImgPath(List<BcImg> bcImgList) {
        for (BcImg bcImg : bcImgList) {
            bcImg.setImgUrl(OSSUtil.getImageURL(bcImg.getImgUrl()));
        }
        return bcImgList;
    }

    /**
     * 评论加上图片
     *
     * @param bcCommentList
     * @param bcImgList
     * @return
     */
    private List<SqComment> commentAddImg(List<SqComment> bcCommentList,
                                          List<BcImg> bcImgList) {
        for (SqComment bcComment : bcCommentList) {
            for (BcImg bcImg : bcImgList) {
                {
                    if (bcComment.getId().equals(bcImg.getCommentId())) {
                        bcComment.getBcImgList().add(bcImg);
                    }

                }
            }
        }
        return bcCommentList;
    }

    /**
     * 评论列表add子评论列表
     *
     * @param sqCommentList
     * @param commentToCommentList
     * @return
     */
    private List<SqComment> commentAddSonComment(
            List<SqComment> sqCommentList, List<SqComment> commentToCommentList, String isFor) {

        for (SqComment sqComment : sqCommentList) {
            for (SqComment sqCommentSon : commentToCommentList) {
                if (sqComment.getId().equals(sqCommentSon.getParentId())) {
                    sqComment.setSonCommentNum(sqComment.getSonCommentNum() + 1);
                    if (sqComment.getSqCommentList().size() > 1 && "app".equals(isFor))
                        continue;
                    sqComment.getSqCommentList().add(sqCommentSon);

                }
            }
        }
        return sqCommentList;
    }

}
