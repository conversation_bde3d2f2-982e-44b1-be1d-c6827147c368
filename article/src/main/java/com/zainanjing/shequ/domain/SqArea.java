package com.zainanjing.shequ.domain;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.zainanjing.shequ.dto.LinkObj;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 社区分区对象 sq_area
 *
 * <AUTHOR>
 * @date 2024-06-04
 */
@Data
@TableName(value = "sq_area")
public class SqArea implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 分区ID
     */
    @TableId
    private Integer id;

    /**
     * 分区名称
     */
    private String name;

    /**
     * 标题颜色
     */
    private String color;

    /**
     * 是否展开 1是0否
     */
    private Integer isExtend;

    /**
     * 是否显示 1正常0关闭
     */
    private Integer isShow;

    /**
     * 排序
     */
    private Long sort;

    /**
     * 状态 1删除 0 正常
     */
    private Integer status;

    /**
     * 创建时间
     */
    private Long createTime;

    /**
     * 更新时间
     */
    private Long updateTime;


    @TableField(exist = false)
    private List<SqForum> sqForumList;
}
